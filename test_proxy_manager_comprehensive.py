# -*- coding: utf-8 -*-
"""
ProcessProxy 代理管理器综合测试文件
=====================================

该测试文件专门验证 proxy_manager.py 中代理管理器的核心功能：
1. 跨进程方法调用测试 - 验证子进程能够通过代理直接调用主进程中的方法
2. 跨进程类实例访问测试 - 验证子进程能够访问和操作主进程中的类实例
3. 代理机制验证测试 - 验证代理对象的创建和管理，确认内存共享机制

作者：Augment Agent
版本：1.0.0
"""

import multiprocessing
import time
import threading
import uuid
import os
import sys
import traceback
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入被测试的模块
from global_tools.utils.enhanced_process.proxy_manager import (
    ProcessProxyManager, ProcessProxy, ProxyConfiguration,
    create_proxy_manager, create_proxy
)
from global_tools.utils import Logger, Colors, ClassInstanceManager

# 获取日志记录器
logger: Logger = ClassInstanceManager.get_instance("ProxyTestLogger")


@dataclass
class TestResult:
    """测试结果数据类"""
    test_name: str
    success: bool
    message: str
    execution_time: float
    details: Dict[str, Any] = None


class TestService:
    """
    测试服务类 - 用于验证跨进程代理功能
    
    该类包含各种类型的方法和属性，用于全面测试代理机制：
    - 简单方法调用和复杂操作
    - 属性读取和修改
    - 状态管理和数据同步
    - 引用验证机制
    """
    
    def __init__(self):
        """初始化测试服务"""
        self.__counter = 0  # 私有计数器
        self.data = {"status": "initialized", "calls": []}  # 公有数据
        self.instance_id = str(uuid.uuid4())  # 实例唯一标识
        self.__modification_log = []  # 修改日志
        self.__lock = threading.Lock()  # 线程锁
        
        logger.info(f"测试服务已初始化，实例ID: {self.instance_id}", color=Colors.INFO)
    
    def increment(self) -> int:
        """
        递增计数器方法
        
        Returns:
            int: 递增后的计数值
            
        使用示例：
            result = service.increment()  # 返回递增后的值
        """
        with self.__lock:
            self.__counter += 1
            self.data["calls"].append(f"increment_{self.__counter}")
            self.__modification_log.append({
                "action": "increment",
                "timestamp": time.time(),
                "process_id": os.getpid(),
                "counter_value": self.__counter
            })
            
            logger.debug(f"计数器递增: {self.__counter} (进程: {os.getpid()})", color=Colors.DEBUG)
            return self.__counter
    
    def get_count(self) -> int:
        """
        获取当前计数值
        
        Returns:
            int: 当前计数值
        """
        return self.__counter
    
    def set_status(self, status: str):
        """
        设置状态
        
        Args:
            status (str): 新状态值
        """
        with self.__lock:
            old_status = self.data.get("status")
            self.data["status"] = status
            self.__modification_log.append({
                "action": "set_status",
                "timestamp": time.time(),
                "process_id": os.getpid(),
                "old_status": old_status,
                "new_status": status
            })
            
            logger.debug(f"状态已更新: {old_status} -> {status} (进程: {os.getpid()})", color=Colors.DEBUG)
    
    def get_data(self) -> Dict[str, Any]:
        """
        获取完整数据
        
        Returns:
            Dict[str, Any]: 完整的数据字典
        """
        return self.data.copy()
    
    def add_data_entry(self, key: str, value: Any):
        """
        添加数据条目
        
        Args:
            key (str): 数据键
            value (Any): 数据值
        """
        with self.__lock:
            self.data[key] = value
            self.__modification_log.append({
                "action": "add_data_entry",
                "timestamp": time.time(),
                "process_id": os.getpid(),
                "key": key,
                "value": value
            })
            
            logger.debug(f"数据条目已添加: {key} = {value} (进程: {os.getpid()})", color=Colors.DEBUG)
    
    def get_instance_id(self) -> str:
        """
        获取实例ID - 用于验证是否操作同一个实例
        
        Returns:
            str: 实例唯一标识
        """
        return self.instance_id
    
    def get_modification_log(self) -> List[Dict[str, Any]]:
        """
        获取修改日志 - 用于验证跨进程操作记录
        
        Returns:
            List[Dict[str, Any]]: 修改日志列表
        """
        return self.__modification_log.copy()
    
    def complex_operation(self, data: Dict[str, Any], multiplier: int = 2) -> Dict[str, Any]:
        """
        复杂操作方法 - 测试复杂参数传递和处理
        
        Args:
            data (Dict[str, Any]): 输入数据
            multiplier (int): 乘数，默认为2
            
        Returns:
            Dict[str, Any]: 处理后的结果
        """
        with self.__lock:
            result = {
                "input_data": data,
                "multiplier": multiplier,
                "processed_at": time.time(),
                "process_id": os.getpid(),
                "instance_id": self.instance_id,
                "counter_at_processing": self.__counter
            }
            
            # 如果输入数据包含数值，进行计算
            if "value" in data and isinstance(data["value"], (int, float)):
                result["calculated_value"] = data["value"] * multiplier
            
            self.__modification_log.append({
                "action": "complex_operation",
                "timestamp": time.time(),
                "process_id": os.getpid(),
                "input_data": data,
                "result": result
            })
            
            logger.debug(f"复杂操作完成 (进程: {os.getpid()})", color=Colors.DEBUG)
            return result
    
    def verify_reference_not_copy(self) -> Dict[str, Any]:
        """
        验证引用而非拷贝的方法
        
        该方法返回包含内存地址和实例信息的数据，用于验证跨进程访问是通过引用实现的。
        
        Returns:
            Dict[str, Any]: 包含验证信息的字典
        """
        return {
            "instance_id": self.instance_id,
            "object_id": id(self),
            "counter_value": self.__counter,
            "data_id": id(self.data),
            "process_id": os.getpid(),
            "timestamp": time.time(),
            "modification_count": len(self.__modification_log)
        }


class ProxyManagerTester:
    """
    代理管理器测试器
    
    该类负责执行所有的代理管理器测试，包括：
    - 跨进程方法调用测试
    - 跨进程类实例访问测试  
    - 代理机制验证测试
    """
    
    def __init__(self):
        """初始化测试器"""
        self.__test_results = []  # 测试结果列表
        self.__manager = None  # 代理管理器
        self.__test_service = None  # 测试服务实例
        
        logger.info("代理管理器测试器已初始化", color=Colors.INFO)
    
    def setup_test_environment(self) -> bool:
        """
        设置测试环境
        
        Returns:
            bool: 设置是否成功
        """
        try:
            # 创建测试服务实例
            self.__test_service = TestService()
            
            # 创建代理管理器配置
            config = ProxyConfiguration()
            config.debug_mode = True
            config.performance_monitoring = True
            config.optimize_for_scenario('debug')
            
            # 创建并启动代理管理器
            self.__manager = ProcessProxyManager(config=config)
            self.__manager.start()
            
            # 注册测试服务
            self.__manager.register_object('test_service', self.__test_service)
            
            logger.info(f"测试环境设置完成，管理器地址: {self.__manager.address}", color=Colors.SUCCESS)
            return True
            
        except Exception as e:
            logger.error(f"设置测试环境失败: {e}", color=Colors.ERROR)
            traceback.print_exc()
            return False
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        try:
            if self.__manager:
                self.__manager.shutdown()
            logger.info("测试环境已清理", color=Colors.INFO)
        except Exception as e:
            logger.error(f"清理测试环境失败: {e}", color=Colors.ERROR)

    def run_cross_process_method_call_test(self) -> TestResult:
        """
        跨进程方法调用测试

        验证子进程能够通过代理直接调用主进程中的方法，
        确认调用的是原始方法实例而非拷贝版本。

        Returns:
            TestResult: 测试结果
        """
        test_name = "跨进程方法调用测试"
        start_time = time.time()

        try:
            logger.info(f"开始执行 {test_name}", color=Colors.INFO)

            # 创建子进程进行测试
            def child_process_worker(manager_address, manager_authkey):
                """子进程工作函数"""
                try:
                    # 连接到主进程的代理管理器
                    child_manager = ProcessProxyManager(
                        address=manager_address,
                        authkey=manager_authkey
                    )
                    child_manager.connect()

                    # 创建代理对象
                    service_proxy = child_manager.create_proxy('test_service')

                    # 测试1: 简单方法调用
                    initial_count = service_proxy.get_count()
                    logger.debug(f"子进程获取初始计数: {initial_count}", color=Colors.DEBUG)

                    # 测试2: 方法调用并验证返回值
                    result1 = service_proxy.increment()
                    result2 = service_proxy.increment()
                    result3 = service_proxy.increment()

                    logger.debug(f"子进程调用increment结果: {result1}, {result2}, {result3}", color=Colors.DEBUG)

                    # 测试3: 复杂方法调用
                    complex_data = {"value": 10, "name": "test_data"}
                    complex_result = service_proxy.complex_operation(complex_data, multiplier=3)

                    logger.debug(f"子进程复杂操作结果: {complex_result}", color=Colors.DEBUG)

                    # 测试4: 验证实例ID一致性
                    instance_id = service_proxy.get_instance_id()
                    logger.debug(f"子进程获取实例ID: {instance_id}", color=Colors.DEBUG)

                    # 将结果写入共享数据
                    shared_data = child_manager.SharedDataManager()
                    with shared_data.get_lock():
                        shared_data.set('child_test_results', {
                            'initial_count': initial_count,
                            'increment_results': [result1, result2, result3],
                            'complex_result': complex_result,
                            'instance_id': instance_id,
                            'child_process_id': os.getpid()
                        })

                    logger.info("子进程测试完成", color=Colors.SUCCESS)

                except Exception as e:
                    logger.error(f"子进程测试失败: {e}", color=Colors.ERROR)
                    traceback.print_exc()

            # 启动子进程
            process = multiprocessing.Process(
                target=child_process_worker,
                args=(self.__manager.address, self.__manager.authkey)
            )
            process.start()
            process.join(timeout=10)  # 10秒超时

            if process.is_alive():
                process.terminate()
                process.join()
                raise TimeoutError("子进程执行超时")

            # 验证测试结果
            shared_data = self.__manager.SharedDataManager()
            with shared_data.get_lock():
                child_results = shared_data.get('child_test_results')

            if not child_results:
                raise ValueError("未获取到子进程测试结果")

            # 验证主进程状态
            main_count = self.__test_service.get_count()
            main_instance_id = self.__test_service.get_instance_id()
            main_data = self.__test_service.get_data()

            # 断言验证
            assert child_results['increment_results'] == [1, 2, 3], "递增结果不正确"
            assert main_count == 3, f"主进程计数不正确: 期望3, 实际{main_count}"
            assert child_results['instance_id'] == main_instance_id, "实例ID不一致"
            assert child_results['complex_result']['instance_id'] == main_instance_id, "复杂操作实例ID不一致"
            assert len(main_data['calls']) == 3, f"调用记录数量不正确: 期望3, 实际{len(main_data['calls'])}"

            execution_time = time.time() - start_time

            logger.info(f"{test_name} 执行成功", color=Colors.SUCCESS)
            return TestResult(
                test_name=test_name,
                success=True,
                message="跨进程方法调用测试通过，验证了方法调用的正确性和实例一致性",
                execution_time=execution_time,
                details={
                    'child_results': child_results,
                    'main_count': main_count,
                    'main_instance_id': main_instance_id,
                    'main_data_calls': main_data['calls']
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"{test_name} 执行失败: {e}"
            logger.error(error_msg, color=Colors.ERROR)
            traceback.print_exc()

            return TestResult(
                test_name=test_name,
                success=False,
                message=error_msg,
                execution_time=execution_time,
                details={'error': str(e)}
            )

    def run_cross_process_instance_access_test(self) -> TestResult:
        """
        跨进程类实例访问测试

        验证子进程能够访问和操作主进程中的类实例，
        确认操作的是同一个实例对象而非副本，测试实例状态的实时同步。

        Returns:
            TestResult: 测试结果
        """
        test_name = "跨进程类实例访问测试"
        start_time = time.time()

        try:
            logger.info(f"开始执行 {test_name}", color=Colors.INFO)

            # 记录初始状态
            initial_data = self.__test_service.get_data()
            initial_count = self.__test_service.get_count()
            initial_instance_id = self.__test_service.get_instance_id()

            logger.debug(f"初始状态 - 计数: {initial_count}, 数据: {initial_data}", color=Colors.DEBUG)

            # 创建子进程进行实例访问测试
            def child_instance_worker(manager_address, manager_authkey):
                """子进程实例访问工作函数"""
                try:
                    # 连接到主进程的代理管理器
                    child_manager = ProcessProxyManager(
                        address=manager_address,
                        authkey=manager_authkey
                    )
                    child_manager.connect()

                    # 创建代理对象
                    service_proxy = child_manager.create_proxy('test_service')

                    # 测试1: 属性读取
                    current_data = service_proxy.get_data()
                    current_count = service_proxy.get_count()
                    instance_id = service_proxy.get_instance_id()

                    logger.debug(f"子进程读取 - 计数: {current_count}, 实例ID: {instance_id}", color=Colors.DEBUG)

                    # 测试2: 属性修改
                    service_proxy.set_status("child_modified")
                    service_proxy.add_data_entry("child_key", "child_value")
                    service_proxy.add_data_entry("child_timestamp", time.time())

                    # 测试3: 方法调用修改状态
                    increment_result = service_proxy.increment()
                    logger.debug(f"子进程递增结果: {increment_result}", color=Colors.DEBUG)

                    # 测试4: 验证修改后的状态
                    modified_data = service_proxy.get_data()
                    modified_count = service_proxy.get_count()

                    # 测试5: 获取修改日志
                    modification_log = service_proxy.get_modification_log()

                    # 将结果写入共享数据
                    shared_data = child_manager.SharedDataManager()
                    with shared_data.get_lock():
                        shared_data.set('instance_test_results', {
                            'initial_read': {
                                'data': current_data,
                                'count': current_count,
                                'instance_id': instance_id
                            },
                            'after_modification': {
                                'data': modified_data,
                                'count': modified_count,
                                'increment_result': increment_result
                            },
                            'modification_log_count': len(modification_log),
                            'child_process_id': os.getpid()
                        })

                    logger.info("子进程实例访问测试完成", color=Colors.SUCCESS)

                except Exception as e:
                    logger.error(f"子进程实例访问测试失败: {e}", color=Colors.ERROR)
                    traceback.print_exc()

            # 启动子进程
            process = multiprocessing.Process(
                target=child_instance_worker,
                args=(self.__manager.address, self.__manager.authkey)
            )
            process.start()
            process.join(timeout=10)  # 10秒超时

            if process.is_alive():
                process.terminate()
                process.join()
                raise TimeoutError("子进程执行超时")

            # 验证测试结果
            shared_data = self.__manager.SharedDataManager()
            with shared_data.get_lock():
                instance_results = shared_data.get('instance_test_results')

            if not instance_results:
                raise ValueError("未获取到子进程实例测试结果")

            # 验证主进程当前状态
            final_data = self.__test_service.get_data()
            final_count = self.__test_service.get_count()
            final_instance_id = self.__test_service.get_instance_id()
            final_log = self.__test_service.get_modification_log()

            # 断言验证 - 确认实例状态同步
            assert instance_results['initial_read']['instance_id'] == initial_instance_id, "实例ID不一致"
            assert final_instance_id == initial_instance_id, "主进程实例ID发生变化"
            assert final_data['status'] == "child_modified", f"状态未正确修改: {final_data['status']}"
            assert 'child_key' in final_data, "子进程添加的数据键未同步"
            assert final_data['child_key'] == "child_value", "子进程添加的数据值未正确同步"
            assert final_count > initial_count, f"计数未正确递增: 初始{initial_count}, 最终{final_count}"
            assert len(final_log) > 0, "修改日志为空"

            # 验证修改日志中包含子进程的操作
            child_operations = [log for log in final_log if log.get('process_id') != os.getpid()]
            assert len(child_operations) > 0, "修改日志中未找到子进程操作记录"

            execution_time = time.time() - start_time

            logger.info(f"{test_name} 执行成功", color=Colors.SUCCESS)
            return TestResult(
                test_name=test_name,
                success=True,
                message="跨进程类实例访问测试通过，验证了实例状态的实时同步和一致性",
                execution_time=execution_time,
                details={
                    'instance_results': instance_results,
                    'initial_state': {
                        'count': initial_count,
                        'data': initial_data,
                        'instance_id': initial_instance_id
                    },
                    'final_state': {
                        'count': final_count,
                        'data': final_data,
                        'instance_id': final_instance_id
                    },
                    'child_operations_count': len(child_operations),
                    'total_modifications': len(final_log)
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"{test_name} 执行失败: {e}"
            logger.error(error_msg, color=Colors.ERROR)
            traceback.print_exc()

            return TestResult(
                test_name=test_name,
                success=False,
                message=error_msg,
                execution_time=execution_time,
                details={'error': str(e)}
            )

    def run_proxy_mechanism_verification_test(self) -> TestResult:
        """
        代理机制验证测试

        验证代理对象的创建和管理，确认内存共享机制而非数据拷贝，
        测试代理连接的稳定性和错误处理。

        Returns:
            TestResult: 测试结果
        """
        test_name = "代理机制验证测试"
        start_time = time.time()

        try:
            logger.info(f"开始执行 {test_name}", color=Colors.INFO)

            # 获取初始验证信息
            initial_verification = self.__test_service.verify_reference_not_copy()
            logger.debug(f"初始验证信息: {initial_verification}", color=Colors.DEBUG)

            # 创建子进程进行代理机制验证
            def child_proxy_verification_worker(manager_address, manager_authkey):
                """子进程代理机制验证工作函数"""
                try:
                    # 连接到主进程的代理管理器
                    child_manager = ProcessProxyManager(
                        address=manager_address,
                        authkey=manager_authkey
                    )
                    child_manager.connect()

                    # 创建代理对象
                    service_proxy = child_manager.create_proxy('test_service')

                    # 测试1: 验证引用而非拷贝
                    verification_info = service_proxy.verify_reference_not_copy()
                    logger.debug(f"子进程验证信息: {verification_info}", color=Colors.DEBUG)

                    # 测试2: 多次调用验证一致性
                    verification_info_2 = service_proxy.verify_reference_not_copy()
                    verification_info_3 = service_proxy.verify_reference_not_copy()

                    # 测试3: 修改状态后再次验证
                    service_proxy.increment()
                    service_proxy.set_status("proxy_verification")
                    verification_after_modification = service_proxy.verify_reference_not_copy()

                    # 测试4: 代理对象信息
                    proxy_info = service_proxy._get_proxy_info()

                    # 测试5: 获取管理器统计信息
                    manager_stats = child_manager.get_statistics()

                    # 将结果写入共享数据
                    shared_data = child_manager.SharedDataManager()
                    with shared_data.get_lock():
                        shared_data.set('proxy_verification_results', {
                            'verification_info': verification_info,
                            'verification_info_2': verification_info_2,
                            'verification_info_3': verification_info_3,
                            'verification_after_modification': verification_after_modification,
                            'proxy_info': proxy_info,
                            'manager_stats': manager_stats,
                            'child_process_id': os.getpid()
                        })

                    logger.info("子进程代理机制验证完成", color=Colors.SUCCESS)

                except Exception as e:
                    logger.error(f"子进程代理机制验证失败: {e}", color=Colors.ERROR)
                    traceback.print_exc()

            # 启动子进程
            process = multiprocessing.Process(
                target=child_proxy_verification_worker,
                args=(self.__manager.address, self.__manager.authkey)
            )
            process.start()
            process.join(timeout=10)  # 10秒超时

            if process.is_alive():
                process.terminate()
                process.join()
                raise TimeoutError("子进程执行超时")

            # 验证测试结果
            shared_data = self.__manager.SharedDataManager()
            with shared_data.get_lock():
                proxy_results = shared_data.get('proxy_verification_results')

            if not proxy_results:
                raise ValueError("未获取到子进程代理验证结果")

            # 获取最终验证信息
            final_verification = self.__test_service.verify_reference_not_copy()

            # 断言验证 - 确认引用而非拷贝
            child_verification = proxy_results['verification_info']
            assert child_verification['instance_id'] == initial_verification['instance_id'], "实例ID不一致，可能是拷贝而非引用"
            assert child_verification['instance_id'] == final_verification['instance_id'], "最终实例ID不一致"

            # 验证多次调用的一致性
            assert proxy_results['verification_info_2']['instance_id'] == child_verification['instance_id'], "多次调用实例ID不一致"
            assert proxy_results['verification_info_3']['instance_id'] == child_verification['instance_id'], "多次调用实例ID不一致"

            # 验证修改后的状态同步
            after_mod = proxy_results['verification_after_modification']
            assert after_mod['instance_id'] == child_verification['instance_id'], "修改后实例ID不一致"
            assert after_mod['counter_value'] > child_verification['counter_value'], "计数器未正确递增"
            assert after_mod['modification_count'] > child_verification['modification_count'], "修改计数未正确更新"

            # 验证代理信息
            proxy_info = proxy_results['proxy_info']
            assert proxy_info['obj_id'] == 'test_service', "代理对象ID不正确"
            assert proxy_info['initialized'] == True, "代理对象未正确初始化"

            # 验证管理器统计信息
            manager_stats = proxy_results['manager_stats']
            assert 'test_service' in manager_stats['object_list'], "管理器统计中未找到测试服务"
            assert manager_stats['registered_objects'] >= 1, "注册对象数量不正确"

            execution_time = time.time() - start_time

            logger.info(f"{test_name} 执行成功", color=Colors.SUCCESS)
            return TestResult(
                test_name=test_name,
                success=True,
                message="代理机制验证测试通过，确认了引用机制而非拷贝，代理连接稳定",
                execution_time=execution_time,
                details={
                    'proxy_results': proxy_results,
                    'initial_verification': initial_verification,
                    'final_verification': final_verification,
                    'instance_consistency': True,
                    'state_synchronization': True
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"{test_name} 执行失败: {e}"
            logger.error(error_msg, color=Colors.ERROR)
            traceback.print_exc()

            return TestResult(
                test_name=test_name,
                success=False,
                message=error_msg,
                execution_time=execution_time,
                details={'error': str(e)}
            )

    def run_all_tests(self) -> List[TestResult]:
        """
        运行所有测试

        Returns:
            List[TestResult]: 所有测试结果列表
        """
        logger.info("开始执行代理管理器综合测试", color=Colors.INFO)

        # 设置测试环境
        if not self.setup_test_environment():
            return [TestResult(
                test_name="环境设置",
                success=False,
                message="测试环境设置失败",
                execution_time=0.0
            )]

        try:
            # 执行所有测试
            test_methods = [
                self.run_cross_process_method_call_test,
                self.run_cross_process_instance_access_test,
                self.run_proxy_mechanism_verification_test
            ]

            for test_method in test_methods:
                try:
                    result = test_method()
                    self.__test_results.append(result)

                    # 短暂等待，确保进程间通信稳定
                    time.sleep(0.5)

                except Exception as e:
                    error_result = TestResult(
                        test_name=test_method.__name__,
                        success=False,
                        message=f"测试执行异常: {e}",
                        execution_time=0.0,
                        details={'error': str(e)}
                    )
                    self.__test_results.append(error_result)
                    logger.error(f"测试方法 {test_method.__name__} 执行异常: {e}", color=Colors.ERROR)

            return self.__test_results

        finally:
            # 清理测试环境
            self.cleanup_test_environment()

    def generate_test_report(self) -> str:
        """
        生成测试报告

        Returns:
            str: 格式化的测试报告
        """
        if not self.__test_results:
            return "没有测试结果可报告"

        total_tests = len(self.__test_results)
        passed_tests = sum(1 for result in self.__test_results if result.success)
        failed_tests = total_tests - passed_tests
        total_time = sum(result.execution_time for result in self.__test_results)

        report_lines = [
            "=" * 80,
            "ProcessProxy 代理管理器综合测试报告",
            "=" * 80,
            f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"总测试数: {total_tests}",
            f"通过测试: {passed_tests}",
            f"失败测试: {failed_tests}",
            f"成功率: {(passed_tests/total_tests)*100:.1f}%",
            f"总执行时间: {total_time:.2f}秒",
            "",
            "详细测试结果:",
            "-" * 80
        ]

        for i, result in enumerate(self.__test_results, 1):
            status = "✅ 通过" if result.success else "❌ 失败"
            report_lines.extend([
                f"{i}. {result.test_name} - {status}",
                f"   执行时间: {result.execution_time:.2f}秒",
                f"   结果信息: {result.message}",
                ""
            ])

            if not result.success and result.details:
                report_lines.append(f"   错误详情: {result.details.get('error', 'N/A')}")
                report_lines.append("")

        report_lines.extend([
            "-" * 80,
            "测试总结:",
            ""
        ])

        if failed_tests == 0:
            report_lines.append("🎉 所有测试均通过！代理管理器功能正常。")
        else:
            report_lines.append(f"⚠️  有 {failed_tests} 个测试失败，请检查相关功能。")

        report_lines.extend([
            "",
            "核心功能验证结果:",
            f"- 跨进程方法调用: {'✅' if any('方法调用' in r.test_name and r.success for r in self.__test_results) else '❌'}",
            f"- 跨进程实例访问: {'✅' if any('实例访问' in r.test_name and r.success for r in self.__test_results) else '❌'}",
            f"- 代理机制验证: {'✅' if any('机制验证' in r.test_name and r.success for r in self.__test_results) else '❌'}",
            "",
            "=" * 80
        ])

        return "\n".join(report_lines)


def main():
    """
    主测试函数

    执行所有代理管理器测试并生成报告。
    """
    print("ProcessProxy 代理管理器综合测试")
    print("=" * 50)

    try:
        # 创建测试器
        tester = ProxyManagerTester()

        # 运行所有测试
        results = tester.run_all_tests()

        # 生成并显示测试报告
        report = tester.generate_test_report()
        print(report)

        # 返回测试结果
        all_passed = all(result.success for result in results)
        exit_code = 0 if all_passed else 1

        print(f"\n测试完成，退出码: {exit_code}")
        return exit_code

    except Exception as e:
        print(f"测试执行失败: {e}")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    # 设置多进程启动方法
    multiprocessing.set_start_method('spawn', force=True)

    # 执行测试
    exit_code = main()
    sys.exit(exit_code)
