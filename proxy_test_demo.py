# -*- coding: utf-8 -*-
"""
ProcessProxy 代理管理器演示脚本
============================

该脚本演示代理管理器的核心功能，展示跨进程对象访问的基本用法。

作者：Augment Agent
版本：1.0.0
"""

import multiprocessing
import time
import os
import sys

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from global_tools.utils.enhanced_process.proxy_manager import (
    ProcessProxyManager, ProxyConfiguration
)


class DemoService:
    """演示服务类"""
    
    def __init__(self):
        self.counter = 0
        self.data = {"status": "initialized"}
        self.process_id = os.getpid()
        print(f"📦 DemoService 在进程 {self.process_id} 中创建")
    
    def increment(self):
        """递增计数器"""
        self.counter += 1
        print(f"🔢 计数器递增到 {self.counter} (进程: {os.getpid()})")
        return self.counter
    
    def get_info(self):
        """获取服务信息"""
        info = {
            "counter": self.counter,
            "data": self.data,
            "created_in_process": self.process_id,
            "accessed_from_process": os.getpid()
        }
        print(f"ℹ️  服务信息: {info}")
        return info
    
    def set_status(self, status):
        """设置状态"""
        old_status = self.data.get("status")
        self.data["status"] = status
        print(f"📝 状态更新: {old_status} -> {status} (进程: {os.getpid()})")


def child_worker(manager_address, manager_authkey):
    """子进程工作函数"""
    print(f"\n🚀 子进程 {os.getpid()} 启动")
    
    try:
        # 连接到主进程的代理管理器
        print("🔗 连接到代理管理器...")
        manager = ProcessProxyManager(
            address=manager_address,
            authkey=manager_authkey
        )
        manager.connect()
        print("✅ 代理管理器连接成功")
        
        # 创建代理对象
        print("🎭 创建代理对象...")
        service_proxy = manager.create_proxy('demo_service')
        print("✅ 代理对象创建成功")
        
        # 通过代理调用方法
        print("\n📞 开始跨进程方法调用...")
        
        # 获取初始信息
        info = service_proxy.get_info()
        print(f"📊 初始信息: 计数器={info['counter']}, 状态={info['data']['status']}")
        
        # 递增计数器
        print("⬆️  递增计数器...")
        result1 = service_proxy.increment()
        result2 = service_proxy.increment()
        print(f"📈 递增结果: {result1}, {result2}")
        
        # 修改状态
        print("🔄 修改状态...")
        service_proxy.set_status("child_modified")
        
        # 获取最终信息
        final_info = service_proxy.get_info()
        print(f"📊 最终信息: 计数器={final_info['counter']}, 状态={final_info['data']['status']}")
        
        print(f"✅ 子进程 {os.getpid()} 完成所有操作")
        
    except Exception as e:
        print(f"❌ 子进程错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主演示函数"""
    print("🎯 ProcessProxy 代理管理器演示")
    print("=" * 50)
    
    try:
        # 设置多进程启动方法
        multiprocessing.set_start_method('spawn', force=True)
        
        # 创建演示服务
        print("🏗️  创建演示服务...")
        demo_service = DemoService()
        
        # 创建并启动代理管理器
        print("🚀 启动代理管理器...")
        config = ProxyConfiguration()
        config.debug_mode = True
        
        manager = ProcessProxyManager(config=config)
        manager.start()
        print(f"✅ 代理管理器已启动，地址: {manager.address}")
        
        # 注册演示服务
        print("📝 注册演示服务...")
        manager.register_object('demo_service', demo_service)
        print("✅ 服务注册完成")
        
        # 在主进程中测试服务
        print("\n🏠 主进程测试:")
        demo_service.increment()
        demo_service.set_status("main_process_ready")
        main_info = demo_service.get_info()
        
        # 启动子进程
        print(f"\n🌟 启动子进程进行跨进程测试...")
        process = multiprocessing.Process(
            target=child_worker,
            args=(manager.address, manager.authkey)
        )
        process.start()
        process.join(timeout=15)  # 15秒超时
        
        if process.is_alive():
            print("⏰ 子进程超时，强制终止")
            process.terminate()
            process.join()
        
        # 检查最终状态
        print(f"\n🔍 检查最终状态 (主进程 {os.getpid()}):")
        final_info = demo_service.get_info()
        
        print("\n📋 演示结果总结:")
        print(f"   - 初始计数器: {main_info['counter']}")
        print(f"   - 最终计数器: {final_info['counter']}")
        print(f"   - 最终状态: {final_info['data']['status']}")
        print(f"   - 服务创建进程: {final_info['created_in_process']}")
        print(f"   - 当前访问进程: {final_info['accessed_from_process']}")
        
        # 验证跨进程操作
        if final_info['counter'] > main_info['counter']:
            print("✅ 跨进程方法调用成功！子进程成功修改了主进程中的对象状态")
        else:
            print("❌ 跨进程方法调用可能失败")
        
        if final_info['data']['status'] == "child_modified":
            print("✅ 跨进程状态修改成功！子进程成功修改了主进程中的对象属性")
        else:
            print("❌ 跨进程状态修改可能失败")
        
        # 关闭管理器
        print("\n🛑 关闭代理管理器...")
        manager.shutdown()
        print("✅ 演示完成")
        
        return 0
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    print(f"\n演示结束，退出码: {exit_code}")
    input("按回车键退出...")
    sys.exit(exit_code)
