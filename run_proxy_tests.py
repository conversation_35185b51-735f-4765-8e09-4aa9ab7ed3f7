# -*- coding: utf-8 -*-
"""
代理管理器测试运行脚本
===================

该脚本用于运行 ProcessProxy 代理管理器的综合测试。

使用方法：
    python run_proxy_tests.py

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import multiprocessing

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("=" * 60)
    print("ProcessProxy 代理管理器测试启动器")
    print("=" * 60)
    
    try:
        # 设置多进程启动方法
        multiprocessing.set_start_method('spawn', force=True)
        print("✅ 多进程环境已配置")
        
        # 导入并运行测试
        from test_proxy_manager_comprehensive import main as test_main
        
        print("🚀 开始执行测试...")
        exit_code = test_main()
        
        if exit_code == 0:
            print("\n🎉 所有测试通过！")
        else:
            print("\n❌ 部分测试失败，请查看详细报告")
        
        return exit_code
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖模块都已正确安装")
        return 1
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)
